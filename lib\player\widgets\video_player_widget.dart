import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import '../../models/preset_item.dart';

class VideoPlayerWidget extends StatefulWidget {
  final PresetItem videoItem;

  const VideoPlayerWidget({
    super.key,
    required this.videoItem,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late BetterPlayerController _betterPlayerController;

  @override
  void initState() {
    super.initState();

    final isNetwork = widget.videoItem.url.startsWith('http');
    final isHLS = widget.videoItem.url.contains('.m3u8');

    final dataSource = BetterPlayerDataSource(
      isNetwork
          ? BetterPlayerDataSourceType.network
          : BetterPlayerDataSourceType.file,
      widget.videoItem.url,
      liveStream: isHLS,
    );

    _betterPlayerController = BetterPlayerController(
      BetterPlayerConfiguration(
        autoPlay: true,
        looping: false,
        aspectRatio: 16 / 9,
        controlsConfiguration: const BetterPlayerControlsConfiguration(
          enableMute: true,
          enablePlaybackSpeed: true,
        ),
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 48),
                const SizedBox(height: 8),
                const Text('Error playing video',
                    style: TextStyle(color: Colors.white)),
                const SizedBox(height: 4),
                Text(
                  errorMessage ?? 'Unknown error',
                  style: const TextStyle(color: Colors.white70),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      ),
      betterPlayerDataSource: dataSource,
    );
  }

  @override
  void dispose() {
    _betterPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: BetterPlayer(controller: _betterPlayerController),
    );
  }
}
