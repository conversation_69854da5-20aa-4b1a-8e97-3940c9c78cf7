import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import '../../models/preset_item.dart';
import '../controllers/custom_player_controller.dart';
import '../themes/player_theme.dart';
import 'custom_controls.dart';

class VideoPlayerWidget extends StatefulWidget {
  final PresetItem videoItem;

  const VideoPlayerWidget({
    super.key,
    required this.videoItem,
  });

  @override
  State<VideoPlayerWidget> createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late BetterPlayerController _betterPlayerController;
  late CustomPlayerController _customController;

  @override
  void initState() {
    super.initState();
    _customController = CustomPlayerController();
    _setupPlayer();
  }

  void _setupPlayer() {
    final isNetwork = widget.videoItem.url.startsWith('http');
    final isHLS = widget.videoItem.url.contains('.m3u8');

    final dataSource = BetterPlayerDataSource(
      isNetwork
          ? BetterPlayerDataSourceType.network
          : BetterPlayerDataSourceType.file,
      widget.videoItem.url,
      liveStream: isHLS,
      headers: {
        if (widget.videoItem.agent != null)
          'User-Agent': widget.videoItem.agent!,
        if (widget.videoItem.referer != null)
          'Referer': widget.videoItem.referer!,
      },
    );

    _betterPlayerController = BetterPlayerController(
      BetterPlayerConfiguration(
        autoPlay: true,
        looping: false,
        aspectRatio: 16 / 9,
        // Disable default controls since we're using custom ones
        controlsConfiguration: const BetterPlayerControlsConfiguration(
          showControls: false,
        ),
        // Custom error handling through our controller
        errorBuilder: (context, errorMessage) {
          return Container(
            color: PlayerTheme.backgroundColor,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline_rounded,
                    color: PlayerTheme.errorColor,
                    size: 64,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load video',
                    style: PlayerTheme.errorTextStyle,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage ?? 'Unknown error occurred',
                    style: PlayerTheme.subtitleTextStyle,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
      betterPlayerDataSource: dataSource,
    );

    // Initialize our custom controller with the BetterPlayer controller
    _customController.initialize(_betterPlayerController);
  }

  @override
  void dispose() {
    _customController.dispose();
    _betterPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: PlayerTheme.backgroundColor,
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: Stack(
          children: [
            // Video player
            BetterPlayer(controller: _betterPlayerController),
            // Custom controls overlay
            CustomControls(
              controller: _customController,
              title: widget.videoItem.name,
              showTitle: true,
              onSettingsPressed: () {
                _showSettingsDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Theme(
        data: PlayerTheme.themeData,
        child: AlertDialog(
          title: const Text('Player Settings'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Playback speed
              ListTile(
                leading: const Icon(PlayerTheme.speedIcon),
                title: const Text('Playback Speed'),
                subtitle: Text('${_customController.playbackSpeed}x'),
                onTap: () => _showSpeedDialog(context),
              ),
              // Volume
              ListTile(
                leading: Icon(
                  _customController.isMuted
                      ? PlayerTheme.volumeOffIcon
                      : PlayerTheme.volumeUpIcon,
                ),
                title: const Text('Volume'),
                subtitle: Slider(
                  value: _customController.volume,
                  onChanged: _customController.setVolume,
                  divisions: 20,
                  label: '${(_customController.volume * 100).round()}%',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSpeedDialog(BuildContext context) {
    final speeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

    showDialog(
      context: context,
      builder: (context) => Theme(
        data: PlayerTheme.themeData,
        child: AlertDialog(
          title: const Text('Playback Speed'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: speeds.map((speed) {
              return RadioListTile<double>(
                title: Text('${speed}x'),
                value: speed,
                groupValue: _customController.playbackSpeed,
                onChanged: (value) {
                  if (value != null) {
                    _customController.setPlaybackSpeed(value);
                    Navigator.of(context).pop();
                    Navigator.of(context).pop(); // Close settings dialog too
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }
}
