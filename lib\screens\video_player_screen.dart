import 'package:cat_player/models/preset_item.dart';
import 'package:cat_player/player/widgets/video_player_widget.dart';
import 'package:flutter/material.dart';

class VideoPlayerScreen extends StatelessWidget {
  final PresetItem videoItem;

  const VideoPlayerScreen({super.key, required this.videoItem});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(videoItem.name),
      ),
      body: Center(
        child: VideoPlayerWidget(videoItem: videoItem),
      ),
    );
  }
}
