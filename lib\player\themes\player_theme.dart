import 'package:flutter/material.dart';

/// Custom theme configuration for the video player
class PlayerTheme {
  // Primary colors
  static const Color primaryColor = Color(0xFF6C63FF);
  static const Color primaryColorDark = Color(0xFF5A52E8);
  static const Color accentColor = Color(0xFF00D4AA);

  // Background colors
  static const Color backgroundColor = Color(0xFF1A1A1A);
  static const Color surfaceColor = Color(0xFF2D2D2D);
  static const Color overlayColor = Color(0x80000000);

  // Text colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB3B3B3);
  static const Color textDisabled = Color(0xFF666666);

  // Control colors
  static const Color controlBackground = Color(0x66000000);
  static const Color controlBackgroundActive = Color(0x99000000);
  static const Color buttonColor = Color(0xFFFFFFFF);
  static const Color buttonColorActive = primaryColor;
  static const Color buttonColorDisabled = Color(0xFF666666);

  // Progress colors
  static const Color progressBackground = Color(0x33FFFFFF);
  static const Color progressBuffered = Color(0x66FFFFFF);
  static const Color progressPlayed = primaryColor;
  static const Color progressThumb = primaryColor;

  // Error colors
  static const Color errorColor = Color(0xFFFF5252);
  static const Color warningColor = Color(0xFFFF9800);

  // Dimensions
  static const double controlBarHeight = 60.0;
  static const double buttonSize = 48.0;
  static const double smallButtonSize = 36.0;
  static const double iconSize = 24.0;
  static const double smallIconSize = 18.0;
  static const double borderRadius = 8.0;
  static const double progressBarHeight = 4.0;
  static const double progressThumbSize = 16.0;

  // Animation durations
  static const Duration fadeInDuration = Duration(milliseconds: 300);
  static const Duration fadeOutDuration = Duration(milliseconds: 500);
  static const Duration controlsHideDelay = Duration(seconds: 3);

  // Typography
  static const TextStyle titleTextStyle = TextStyle(
    color: textPrimary,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle subtitleTextStyle = TextStyle(
    color: textSecondary,
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle timeTextStyle = TextStyle(
    color: textPrimary,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    fontFamily: 'monospace',
  );

  static const TextStyle errorTextStyle = TextStyle(
    color: errorColor,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  // Custom icons
  static const IconData playIcon = Icons.play_arrow_rounded;
  static const IconData pauseIcon = Icons.pause_rounded;
  static const IconData replayIcon = Icons.replay_rounded;
  static const IconData volumeUpIcon = Icons.volume_up_rounded;
  static const IconData volumeOffIcon = Icons.volume_off_rounded;
  static const IconData fullscreenIcon = Icons.fullscreen_rounded;
  static const IconData fullscreenExitIcon = Icons.fullscreen_exit_rounded;
  static const IconData settingsIcon = Icons.settings_rounded;
  static const IconData skipNextIcon = Icons.skip_next_rounded;
  static const IconData skipPreviousIcon = Icons.skip_previous_rounded;
  static const IconData forward10Icon = Icons.forward_10_rounded;
  static const IconData replay10Icon = Icons.replay_10_rounded;
  static const IconData subtitlesIcon = Icons.subtitles_rounded;
  static const IconData qualityIcon = Icons.hd_rounded;
  static const IconData speedIcon = Icons.speed_rounded;

  // Gradients
  static const LinearGradient controlsGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.transparent,
      Color(0x33000000),
      Color(0x66000000),
      Color(0x99000000),
    ],
  );

  static const LinearGradient topGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x99000000),
      Color(0x66000000),
      Color(0x33000000),
      Colors.transparent,
    ],
  );

  // Box shadows
  static const List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Color(0x33000000),
      blurRadius: 8,
      offset: Offset(0, 2),
    ),
  ];

  static const List<BoxShadow> controlBarShadow = [
    BoxShadow(
      color: Color(0x66000000),
      blurRadius: 16,
      offset: Offset(0, -4),
    ),
  ];

  // Theme data for Material components
  static ThemeData get themeData => ThemeData.dark().copyWith(
        primaryColor: primaryColor,
        colorScheme: const ColorScheme.dark(
          primary: primaryColor,
          secondary: accentColor,
          surface: surfaceColor,
          error: errorColor,
        ),
        sliderTheme: SliderThemeData(
          activeTrackColor: progressPlayed,
          inactiveTrackColor: progressBackground,
          thumbColor: progressThumb,
          overlayColor: primaryColor.withValues(alpha: 0.2),
          trackHeight: progressBarHeight,
          thumbShape: const RoundSliderThumbShape(
            enabledThumbRadius: progressThumbSize / 2,
          ),
        ),
        iconTheme: const IconThemeData(
          color: buttonColor,
          size: iconSize,
        ),
      );
}

/// Extension methods for easier theme access
extension PlayerThemeExtension on BuildContext {
  PlayerTheme get playerTheme => PlayerTheme();
}
