import 'dart:async';
import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';

/// Custom controller that manages video player state and provides additional functionality
class CustomPlayerController extends ChangeNotifier {
  BetterPlayerController? _betterPlayerController;
  Timer? _hideControlsTimer;
  Timer? _positionUpdateTimer;

  // State variables
  bool _isControlsVisible = true;
  bool _isPlaying = false;
  bool _isMuted = false;
  bool _isFullscreen = false;
  bool _isBuffering = false;
  bool _hasError = false;
  String? _errorMessage;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  Duration _bufferedPosition = Duration.zero;

  // Getters
  BetterPlayerController? get betterPlayerController => _betterPlayerController;
  bool get isControlsVisible => _isControlsVisible;
  bool get isPlaying => _isPlaying;
  bool get isMuted => _isMuted;
  bool get isFullscreen => _isFullscreen;
  bool get isBuffering => _isBuffering;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;
  double get volume => _volume;
  double get playbackSpeed => _playbackSpeed;
  Duration get position => _position;
  Duration get duration => _duration;
  Duration get bufferedPosition => _bufferedPosition;

  // Progress as percentage (0.0 to 1.0)
  double get progress {
    if (duration.inMilliseconds == 0) return 0.0;
    return position.inMilliseconds / duration.inMilliseconds;
  }

  double get bufferedProgress {
    if (duration.inMilliseconds == 0) return 0.0;
    return bufferedPosition.inMilliseconds / duration.inMilliseconds;
  }

  /// Initialize the controller with a BetterPlayerController
  void initialize(BetterPlayerController controller) {
    _betterPlayerController = controller;
    _setupListeners();
    _startPositionUpdates();
  }

  /// Setup listeners for the BetterPlayer controller
  void _setupListeners() {
    if (_betterPlayerController == null) return;

    _betterPlayerController!.addEventsListener((event) {
      switch (event.betterPlayerEventType) {
        case BetterPlayerEventType.initialized:
          _updateDuration();
          break;
        case BetterPlayerEventType.play:
          _isPlaying = true;
          _isBuffering = false;
          _hasError = false;
          notifyListeners();
          break;
        case BetterPlayerEventType.pause:
          _isPlaying = false;
          notifyListeners();
          break;
        case BetterPlayerEventType.bufferingStart:
          _isBuffering = true;
          notifyListeners();
          break;
        case BetterPlayerEventType.bufferingEnd:
          _isBuffering = false;
          notifyListeners();
          break;
        case BetterPlayerEventType.exception:
          _hasError = true;
          _errorMessage = event.parameters?['exception']?.toString();
          notifyListeners();
          break;
        case BetterPlayerEventType.finished:
          _isPlaying = false;
          notifyListeners();
          break;
        default:
          break;
      }
    });
  }

  /// Start periodic position updates
  void _startPositionUpdates() {
    _positionUpdateTimer?.cancel();
    _positionUpdateTimer = Timer.periodic(
      const Duration(milliseconds: 100),
      (_) => _updatePosition(),
    );
  }

  /// Update current position and buffered position
  void _updatePosition() async {
    if (_betterPlayerController?.videoPlayerController == null) {
      return;
    }

    final videoController = _betterPlayerController!.videoPlayerController!;
    final newPosition = videoController.value.position;
    final newBufferedPosition = videoController.value.buffered.isNotEmpty
        ? videoController.value.buffered.last.end
        : Duration.zero;

    if (newPosition != _position || newBufferedPosition != _bufferedPosition) {
      _position = newPosition;
      _bufferedPosition = newBufferedPosition;
      notifyListeners();
    }
  }

  /// Update duration from video controller
  void _updateDuration() {
    if (_betterPlayerController?.videoPlayerController != null) {
      final duration =
          _betterPlayerController!.videoPlayerController!.value.duration;
      if (duration != null) {
        _duration = duration;
        notifyListeners();
      }
    }
  }

  /// Play/pause toggle
  void togglePlayPause() {
    if (_betterPlayerController == null) return;

    if (_isPlaying) {
      pause();
    } else {
      play();
    }
  }

  /// Play the video
  void play() {
    _betterPlayerController?.play();
    showControlsTemporarily();
  }

  /// Pause the video
  void pause() {
    _betterPlayerController?.pause();
    showControls();
  }

  /// Toggle mute state
  void toggleMute() {
    if (_betterPlayerController == null) return;

    _isMuted = !_isMuted;
    _betterPlayerController!.setVolume(_isMuted ? 0.0 : _volume);
    notifyListeners();
    showControlsTemporarily();
  }

  /// Set volume (0.0 to 1.0)
  void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
    if (!_isMuted) {
      _betterPlayerController?.setVolume(_volume);
    }
    notifyListeners();
    showControlsTemporarily();
  }

  /// Set playback speed
  void setPlaybackSpeed(double speed) {
    _playbackSpeed = speed;
    _betterPlayerController?.setSpeed(speed);
    notifyListeners();
    showControlsTemporarily();
  }

  /// Seek to specific position
  void seekTo(Duration position) {
    final clampedPosition = Duration(
      milliseconds: position.inMilliseconds.clamp(0, _duration.inMilliseconds),
    );
    _betterPlayerController?.seekTo(clampedPosition);
    showControlsTemporarily();
  }

  /// Seek forward by specified duration
  void seekForward([Duration duration = const Duration(seconds: 10)]) {
    final newPosition = _position + duration;
    seekTo(newPosition);
  }

  /// Seek backward by specified duration
  void seekBackward([Duration duration = const Duration(seconds: 10)]) {
    final newPosition = _position - duration;
    seekTo(newPosition);
  }

  /// Show controls
  void showControls() {
    _hideControlsTimer?.cancel();
    if (!_isControlsVisible) {
      _isControlsVisible = true;
      notifyListeners();
    }
  }

  /// Hide controls
  void hideControls() {
    if (_isControlsVisible) {
      _isControlsVisible = false;
      notifyListeners();
    }
  }

  /// Show controls temporarily (will auto-hide after delay)
  void showControlsTemporarily() {
    showControls();
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), hideControls);
  }

  /// Toggle controls visibility
  void toggleControls() {
    if (_isControlsVisible) {
      hideControls();
    } else {
      showControlsTemporarily();
    }
  }

  /// Enter fullscreen mode
  void enterFullscreen() {
    _betterPlayerController?.enterFullScreen();
    _isFullscreen = true;
    notifyListeners();
  }

  /// Exit fullscreen mode
  void exitFullscreen() {
    _betterPlayerController?.exitFullScreen();
    _isFullscreen = false;
    notifyListeners();
  }

  /// Toggle fullscreen mode
  void toggleFullscreen() {
    if (_isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }

  /// Format duration to readable string (MM:SS or HH:MM:SS)
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _positionUpdateTimer?.cancel();
    super.dispose();
  }
}
